import Link from 'next/link'
import { useNavigation } from '@/contexts/NavigationContext'

export function Footer() {
  const { isNavigationVisible } = useNavigation()

  // Hide footer when navigation is hidden (e.g., in e-reader mode)
  if (!isNavigationVisible) {
    return null
  }

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          
          {/* Brand */}
          <div className="md:col-span-1">
            <h3 className="text-xl font-serif mb-4">OnlyDiary</h3>
            <p className="text-gray-400 text-sm leading-relaxed mb-4">
              Where life becomes income. Turn your most intimate thoughts into your own personal reality show. Monetize your authentic self instantly.
            </p>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>📺 Personal reality show</span>
              <span>•</span>
              <span>💰 Instant monetization</span>
              <span>•</span>
              <span>🔔 Real-time audience</span>
            </div>
          </div>

          {/* Your Reality Show */}
          <div>
            <h4 className="font-semibold mb-4">Your Reality Show</h4>
            <ul className="space-y-2 text-sm">
              <li><Link href="/write" className="text-gray-400 hover:text-white transition-colors">Start Your Show</Link></li>
              <li><Link href="/dashboard" className="text-gray-400 hover:text-white transition-colors">Creator Dashboard</Link></li>
              <li><Link href="/trending" className="text-gray-400 hover:text-white transition-colors">Trending Lives</Link></li>
              <li><Link href="/about" className="text-gray-400 hover:text-white transition-colors">How It Works</Link></li>
            </ul>
          </div>

          {/* Life Monetization */}
          <div>
            <h4 className="font-semibold mb-4">Life Monetization</h4>
            <ul className="space-y-2 text-sm">
              <li><span className="text-gray-400">📺 Personal reality show</span></li>
              <li><span className="text-gray-400">💭 Monetize intimate thoughts</span></li>
              <li><span className="text-gray-400">👥 Real-time audience engagement</span></li>
              <li><span className="text-gray-400">📋 Build anticipation with waitlists</span></li>
              <li><span className="text-gray-400">📸 Share your authentic moments</span></li>
              <li><span className="text-gray-400">✨ Professional presentation</span></li>
            </ul>
          </div>

          {/* Instant Income */}
          <div>
            <h4 className="font-semibold mb-4">Instant Income</h4>
            <ul className="space-y-2 text-sm">
              <li><span className="text-gray-400">💰 80% of subscriber payments</span></li>
              <li><span className="text-gray-400">💝 95% of fan donations</span></li>
              <li><Link href="/dashboard/notifications" className="text-gray-400 hover:text-white transition-colors">Audience notifications (5¢)</Link></li>
              <li><span className="text-gray-400">⚡ Instant cash withdrawals</span></li>
              <li><span className="text-gray-400">📊 Creator analytics</span></li>
            </ul>

            <div className="mt-6 pt-4 border-t border-gray-800">
              <h5 className="font-semibold mb-2 text-xs">Legal</h5>
              <ul className="space-y-1 text-xs">
                <li><Link href="/terms" className="text-gray-500 hover:text-gray-300 transition-colors">Terms of Service</Link></li>
                <li><Link href="/privacy" className="text-gray-500 hover:text-gray-300 transition-colors">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm">
            © {new Date().getFullYear()} OnlyDiary. All rights reserved.
          </div>
          
          <div className="flex items-center gap-6 mt-4 sm:mt-0">
            <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
              Terms
            </Link>
            <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">
              Privacy
            </Link>
            <Link href="/about" className="text-gray-400 hover:text-white text-sm transition-colors">
              About
            </Link>
            <div className="text-gray-400 text-sm">
              Made with ❤️ for writers • Real-time technology • Zero-cost notifications
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
