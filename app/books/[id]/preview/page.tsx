"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { SmartTypography } from "@/components/SmartTypography"
import { useNavigation } from "@/contexts/NavigationContext"

interface Book {
  id: string
  title: string
  cover_image_url: string
  price_amount: number
  preview_chapters: number
  user_id: string
  users: {
    name: string
    avatar: string
    profile_picture_url: string
  }
}

interface Chapter {
  id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
  is_truncated?: boolean
}



export default function BookPreviewPage() {
  const params = useParams()
  const { hideNavigation, showNavigation } = useNavigation()
  const [book, setBook] = useState<Book | null>(null)
  const [previewChapters, setPreviewChapters] = useState<Chapter[]>([])
  const [currentChapterIndex, setCurrentChapterIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  // Hide navigation when preview page loads, show when it unmounts
  useEffect(() => {
    hideNavigation()

    return () => {
      showNavigation()
    }
  }, [hideNavigation, showNavigation])

  useEffect(() => {
    if (params.id) {
      // Add a small delay for mobile devices to ensure proper initialization
      const isMobile = window.innerWidth < 768
      const delay = isMobile ? 100 : 0

      const timer = setTimeout(() => {
        fetchPreviewData()
      }, delay)

      return () => clearTimeout(timer)
    }
  }, [params.id])

  const fetchPreviewData = async () => {
    try {

      // Fetch book details - try by slug first, then by ID
      let bookData = null
      let bookError = null

      // Try by slug first
      const { data: bookBySlug } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          cover_image_url,
          price_amount,
          preview_chapters,
          user_id,
          users!inner(name, avatar, profile_picture_url)
        `)
        .eq('slug', params.id)
        .eq('is_ebook', true)
        .single()

      if (bookBySlug) {
        bookData = bookBySlug
      } else {
        // Try by ID if slug doesn't work
        const { data: bookById, error: idError } = await supabase
          .from('projects')
          .select(`
            id,
            title,
            cover_image_url,
            price_amount,
            preview_chapters,
            user_id,
            users!inner(name, avatar, profile_picture_url)
          `)
          .eq('id', params.id)
          .eq('is_ebook', true)
          .single()

        bookData = bookById
        bookError = idError
      }

      if (bookError || !bookData) {
        setError(`Book not found or not available for preview. Error: ${bookError?.message || 'Book not found'}`)
        return
      }

      // Fix the users array issue - take the first user from the join
      const processedBookData = {
        ...bookData,
        users: Array.isArray(bookData.users) ? bookData.users[0] : bookData.users
      }

      setBook(processedBookData)

      // Fetch all published chapters to filter out title pages
      const { data: allChaptersData, error: chaptersError } = await supabase
        .from('chapters')
        .select('id, title, content, chapter_number, word_count')
        .eq('project_id', bookData.id)
        .eq('is_published', true)
        .order('chapter_number')

      if (chaptersError) {
        setError(`Unable to load preview chapters. Error: ${chaptersError.message}`)
        return
      }

      if (!allChaptersData || allChaptersData.length === 0) {
        setError('No preview chapters available for this book')
        return
      }

      // Show the first few chapters (including title page) but truncate content
      // This gives readers a proper preview experience
      const previewChaptersCount = processedBookData.preview_chapters || 2
      const chaptersToShow = allChaptersData.slice(0, previewChaptersCount)

      // Truncate each chapter to ~400 words for preview
      const truncatedChapters = chaptersToShow.map(chapter => {
        const words = chapter.content.split(/\s+/)
        const isTruncated = words.length > 400
        const truncatedContent = isTruncated
          ? words.slice(0, 400).join(' ') + '...\n\n[Preview continues in full book]'
          : chapter.content

        return {
          ...chapter,
          content: truncatedContent,
          is_truncated: isTruncated
        }
      })

      console.log('🔥 NEW PREVIEW LOGIC WORKING! 🔥')
      console.log('Preview chapters:', {
        totalChapters: allChaptersData.length,
        showingChapters: truncatedChapters.length,
        chapters: truncatedChapters.map(ch => ({
          title: ch.title,
          originalWords: ch.word_count,
          isTruncated: ch.is_truncated,
          newContentLength: ch.content.length
        }))
      })

      setPreviewChapters(truncatedChapters)

    } catch (error) {
      console.error('Error fetching preview data:', error)
      setError(`Failed to load book preview. Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async () => {
    setPurchasing(true)
    try {
      const response = await fetch('/api/books/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookId: params.id,
          priceAmount: book?.price_amount
        }),
      })

      const { url } = await response.json()
      if (url) {
        window.location.href = url
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
    } finally {
      setPurchasing(false)
    }
  }

  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 animate-pulse">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">Preview Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={() => {
                setError(null)
                setLoading(true)
                fetchPreviewData()
              }}
              variant="outline"
            >
              Try Again
            </Button>
            <Link href="/books">
              <Button>Browse Books</Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (!book || previewChapters.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">📚</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">Preview not available</h2>
          <p className="text-gray-600 mb-6">This book doesn't have any preview chapters available.</p>
          <Link href="/books">
            <Button>Browse Books</Button>
          </Link>
        </div>
      </div>
    )
  }

  const currentChapter = previewChapters[currentChapterIndex]

  return (
    <div className="min-h-screen bg-gray-50">
      
      {/* Preview Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex-1">
              <Link
                href={`/books/${book.id}`}
                className="text-white/70 hover:text-white text-sm mb-2 inline-block touch-manipulation"
              >
                ← Back to book details
              </Link>
              <h1 className="text-xl sm:text-2xl font-serif mb-1 break-words">🔥 TESTING CHANGES 🔥 {book.title}</h1>
              <p className="text-white/80 text-sm sm:text-base">by {book.users.name}</p>
            </div>
            <div className="text-left sm:text-right flex-shrink-0">
              <div className="text-sm text-white/70 mb-1">📖 Free Preview</div>
              <div className="text-base sm:text-lg font-semibold">
                {previewChapters.length === 1 ? 'Chapter 1' : `${previewChapters.length} chapters`}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        
        {/* Preview Navigation */}
        {previewChapters.length > 1 && (
          <div className="flex items-center justify-between mb-8 gap-4">
            <Button
              variant="outline"
              onClick={() => setCurrentChapterIndex(prev => Math.max(0, prev - 1))}
              disabled={currentChapterIndex === 0}
              className="touch-manipulation min-h-[44px] px-4"
            >
              ← Previous
            </Button>

            <div className="flex gap-3 flex-1 justify-center">
              {previewChapters.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentChapterIndex(index)}
                  className={`w-6 h-6 sm:w-3 sm:h-3 rounded-full transition-colors touch-manipulation ${
                    index === currentChapterIndex
                      ? 'bg-purple-600'
                      : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500'
                  }`}
                />
              ))}
            </div>

            <Button
              variant="outline"
              onClick={() => setCurrentChapterIndex(prev => Math.min(previewChapters.length - 1, prev + 1))}
              disabled={currentChapterIndex === previewChapters.length - 1}
              className="touch-manipulation min-h-[44px] px-4"
            >
              Next →
            </Button>
          </div>
        )}

        {/* Preview Info */}
        <div className="bg-red-100 border border-red-300 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 text-red-800">
            <span className="text-lg">🔥</span>
            <span className="font-medium">
              TESTING: This should be visible if changes are working! Chapter preview with smart truncation.
            </span>
          </div>
        </div>

        {/* Chapter Content */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8">

          {/* Chapter Header */}
          <div className="p-6 sm:p-8 border-b border-gray-200">
            <div className="text-sm text-purple-600 font-medium mb-2">
              Chapter {currentChapter.chapter_number} • Preview
            </div>
            <h2 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-4">
              {currentChapter.title}
            </h2>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>{currentChapter.word_count.toLocaleString()} words</span>
              <span>•</span>
              <span>{Math.ceil(currentChapter.word_count / 200)} min read</span>
              <span>•</span>
              <span className="text-purple-600 font-medium">Free Preview</span>
            </div>
          </div>

          {/* Chapter Content */}
          <div className="p-6 sm:p-8">
            <SmartTypography
              content={currentChapter.content}
              isPreview={true}
              className="max-w-none"
            />

            {/* Truncation Indicator */}
            {currentChapter.is_truncated && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 text-blue-800 text-sm">
                  <span>📖</span>
                  <span className="font-medium">Preview truncated</span>
                  <span>•</span>
                  <span>Full chapter available in complete book</span>
                </div>
              </div>
            )}

            {/* Preview Fade Effect */}
            <div className="relative mt-8">
              <div className="absolute inset-0 bg-gradient-to-t from-white via-white/80 to-transparent h-24 pointer-events-none"></div>
              <div className="text-center pt-16">
                <div className="inline-flex items-center gap-2 text-gray-500 text-sm mb-4">
                  <span>📖</span>
                  <span>Continue reading with full access</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Purchase Call-to-Action */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-2xl p-8 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-serif text-gray-900 mb-4">
              Enjoying the preview?
            </h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Get instant access to the complete book with all chapters, plus support the author directly. 
              Your purchase helps independent writers continue creating amazing stories.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                onClick={handlePurchase}
                isLoading={purchasing}
                size="lg"
                className="w-full sm:w-auto bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold shadow-lg hover:shadow-xl border-0"
              >
                💰 Buy Full Book for {formatPrice(book.price_amount)}
              </Button>
              
              <Link href={`/books/${book.id}`}>
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  📋 View Details & Reviews
                </Button>
              </Link>
            </div>

            <div className="mt-6 text-sm text-gray-500">
              <div className="flex items-center justify-center gap-4">
                <span>✓ Instant access</span>
                <span>✓ All chapters</span>
                <span>✓ Support the author</span>
                <span>✓ Read anywhere</span>
              </div>
            </div>
          </div>
        </div>

        {/* Preview Stats */}
        <div className="mt-8 grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="text-2xl font-bold text-purple-600">{book.preview_chapters}</div>
            <div className="text-sm text-gray-600">Preview Chapters</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="text-2xl font-bold text-blue-600">
              {previewChapters.reduce((sum, ch) => sum + ch.word_count, 0).toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Preview Words</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="text-2xl font-bold text-green-600">
              {Math.ceil(previewChapters.reduce((sum, ch) => sum + ch.word_count, 0) / 200)}
            </div>
            <div className="text-sm text-gray-600">Minutes Read</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="text-2xl font-bold text-orange-600">Free</div>
            <div className="text-sm text-gray-600">Preview Cost</div>
          </div>
        </div>
      </div>
    </div>
  )
}
